<div class="mb-5 mt-0">
  <app-broker-title [showCreateButton]="false"></app-broker-title>
</div>

<div class="card rounded-4">
  <div class="card-body p-10">
    <div class="stepper stepper-pills d-flex flex-column" id="hotel_unit_rental_stepper">
      <!-- Header with Progress Bar -->
      <div class="mb-5 text-center">
        <h2 class="fw-bold text-dark-blue mb-2">{{ stepNames[currentStep] }}</h2>
        <div class="d-flex justify-content-center align-items-center mb-2">
          <span class="text-success fw-bold">Step {{ currentStep }}</span>
          <span class="text-muted mx-1">of</span>
          <span class="text-muted">{{ totalSteps }}</span>
        </div>
        <div class="progress h-8px bg-light-success w-75 mx-auto">
          <div
            class="progress-bar bg-success"
            role="progressbar"
            [style.width]="(currentStep / totalSteps) * 100 + '%'"
            aria-valuemin="0"
            aria-valuemax="100"
          ></div>
        </div>
      </div>

      <!-- Enhanced Error List Display -->
      <div *ngIf="showErrorList && validationErrors.length > 0" class="alert alert-danger border-0 shadow-sm mb-5" style="border-radius: 12px;">
        <!-- Error Header -->
        <div class="d-flex align-items-center justify-content-between mb-4">
          <div class="d-flex align-items-center">
            <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
              <i class="fas fa-exclamation-triangle text-white"></i>
            </div>
            <div>
              <h5 class="mb-1 text-danger fw-bold">Validation Errors Found</h5>
              <p class="mb-0 text-muted small">Please correct the following {{ validationErrors.length }} issue(s) before proceeding</p>
            </div>
          </div>
        </div>

        <!-- Error Details by Step -->
        <div class="error-steps-container">
          <div *ngFor="let stepError of validationErrors; let stepIndex = index" class="error-step-card mb-3">
            <!-- Step Header -->
            <div class="card border-0 shadow-sm" style="border-radius: 8px;">

              <!-- Error Details -->
              <div class="card-body pt-8">
                <div class="error-list">
                  <div *ngFor="let error of stepError.errors; let errorIndex = index"
                        class="error-item d-flex align-items-start p-3 mb-2 bg-light rounded"
                        [class.border-start]="true"
                        [class.border-danger]="true"
                        [class.border-4]="true">
                    <!-- Error Icon -->
                    <div class="error-icon-container me-3 mt-1">
                      <i class="fas fa-times-circle text-danger"></i>
                    </div>
                    <!-- Error Content -->
                    <div class="error-content flex-grow-1">
                      <div class="d-flex align-items-center mb-1">
                        <strong class="text-dark me-2">{{ error.field | titlecase }}</strong>
                        <span class="badge bg-danger bg-opacity-10 text-danger small">Required</span>
                      </div>
                      <div class="error-messages">
                        <p class="mb-0 text-muted small" *ngFor="let message of error.messages; let last = last">
                          <i class="fas fa-chevron-right me-1" style="font-size: 10px;"></i>
                          {{ message }}<span *ngIf="!last">;</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Content -->
      <form class="mx-auto w-100 pt-5 pb-10" [formGroup]="getCurrentForm()">
        <!-- Step 1: Basic Request Settings -->
        <div *ngIf="currentStep === 1">
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Specialization Scope</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="specializationScopeDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(specializationScopeOptions, getCurrentForm().get('specializationScope')?.value) ||
                  'Select Specialization Scope'
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="specializationScopeDropdown">
                <li *ngFor="let scope of specializationScopeOptions">
                  <a
                    class="dropdown-item"
                    href="javascript:void(0)"
                    (click)="select(getCurrentForm(), 'specializationScope', scope.value); getCurrentForm().get('specializationScope')?.markAsTouched()"
                    >{{ scope.key }}</a
                  >
                </li>
              </ul>
            </div>
            <div
              *ngIf="getCurrentForm().get('specializationScope')?.touched && getCurrentForm().get('specializationScope')?.invalid"
              class="invalid-feedback d-block"
            >
              Specialization Scope is required.
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Type</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="typeDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(getFilteredTypeOptions(), getCurrentForm().get('type')?.value) ||
                  'Select Type'
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="typeDropdown">
                <li *ngFor="let type of getFilteredTypeOptions()">
                  <a
                    class="dropdown-item"
                    href="javascript:void(0)"
                    (click)="select(getCurrentForm(), 'type', type.value); getCurrentForm().get('type')?.markAsTouched()"
                    >{{ type.key }}</a
                  >
                </li>
              </ul>
            </div>
            <div
              *ngIf="getCurrentForm().get('type')?.touched && getCurrentForm().get('type')?.invalid"
              class="invalid-feedback d-block"
            >
              Type is required.
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Unit Type</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="unitTypeDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span>{{
                  getText(getFilteredUnitTypeOptions(), getCurrentForm().get('unitType')?.value) ||
                  'Select Unit Type'
                }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="unitTypeDropdown">
                <li *ngFor="let unitType of getFilteredUnitTypeOptions()">
                  <a
                    class="dropdown-item"
                    href="javascript:void(0)"
                    (click)="select(getCurrentForm(), 'unitType', unitType.value); getCurrentForm().get('unitType')?.markAsTouched()"
                    >{{ unitType.key }}</a
                  >
                </li>
              </ul>
            </div>
            <div
              *ngIf="getCurrentForm().get('unitType')?.touched && getCurrentForm().get('unitType')?.invalid"
              class="invalid-feedback d-block"
            >
              Unit Type is required.
            </div>
          </div>
        </div>

        <!-- Step 2: Location Information -->
        <div *ngIf="currentStep === 2">
          <!-- City Dropdown -->
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">City</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="cityIdDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
                [disabled]="isLoadingCities"
              >
                <span>{{ selectedCityName || 'Select City' }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="cityIdDropdown"
                style="max-height: 200px; overflow-y: auto;"
              >
                <li *ngFor="let city of cities$ | async">
                  <a
                    class="dropdown-item"
                    (click)="onSelectChange('cityId', city.value, city.key)"
                    >{{ city.key }}</a
                  >
                </li>
              </ul>
            </div>
            <div
              *ngIf="getCurrentForm().get('cityId')?.touched && getCurrentForm().get('cityId')?.invalid"
              class="invalid-feedback d-block"
            >
              City is required.
            </div>
            <div *ngIf="isLoadingCities" class="text-muted mt-1">
              <i class="fas fa-spinner fa-spin me-1"></i>
              Loading cities...
            </div>
          </div>

          <!-- Area Dropdown -->
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Area</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="areaIdDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
                [disabled]="!selectedCityId"
              >
                <span>{{ selectedAreaName || 'Select Area' }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="areaIdDropdown"
                style="max-height: 200px; overflow-y: auto;"
              >
                <li *ngFor="let area of areas$ | async">
                  <a
                    class="dropdown-item"
                    (click)="onSelectChange('areaId', area.value, area.key)"
                    >{{ area.key }}</a
                  >
                </li>
              </ul>
            </div>
            <div
              *ngIf="getCurrentForm().get('areaId')?.touched && getCurrentForm().get('areaId')?.invalid"
              class="invalid-feedback d-block"
            >
              Area is required.
            </div>
          </div>

          <!-- Sub-Area Dropdown -->
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Sub Area</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button"
                id="subAreaIdDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
                [disabled]="!selectedAreaId"
              >
                <span>{{ selectedSubAreaName || 'Select Sub Area' }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul
                class="dropdown-menu w-100"
                aria-labelledby="subAreaIdDropdown"
                style="max-height: 200px; overflow-y: auto;"
              >
                <li *ngFor="let subArea of subAreas$ | async">
                  <a
                    class="dropdown-item"
                    (click)="onSelectChange('subAreaId', subArea.value, subArea.key)"
                    >{{ subArea.key }}</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <!-- Enhanced Location Suggestion -->
          <div *ngIf="getInsideCompoundPrivilege() && isClient()" class="mb-10">
            <div class="custom-checkbox-container"
                 [class.has-error]="getCurrentForm().get('locationSuggestions')?.touched && getCurrentForm().get('locationSuggestions')?.invalid">
              <input
                class="custom-checkbox-input"
                type="checkbox"
                id="locationSuggestions"
                formControlName="locationSuggestions"
                aria-describedby="locationSuggestions_help"
                [attr.aria-invalid]="getCurrentForm().get('locationSuggestions')?.touched && getCurrentForm().get('locationSuggestions')?.invalid ? 'true' : 'false'"
              />
              <label class="custom-checkbox-label" for="locationSuggestions">
                <div class="custom-checkbox-box">
                  <i class="fas fa-check custom-checkbox-icon"></i>
                </div>
                <div class="custom-checkbox-content">
                  <span class="custom-checkbox-text">Location Suggestions</span>
                  <small class="custom-checkbox-description">
                    Get personalized location recommendations based on your preferences
                  </small>
                </div>
              </label>
              <!-- Enhanced validation feedback -->
              <div
                *ngIf="getCurrentForm().get('locationSuggestions')?.touched && getCurrentForm().get('locationSuggestions')?.invalid"
                class="checkbox-error-feedback"
                id="locationSuggestions_help"
                role="alert"
              >
                <i class="fas fa-exclamation-circle me-1"></i>
                <span>Location Suggestions selection is required to proceed</span>
              </div>
            </div>
          </div>



          <!-- Compound Name -->
          <div class="mb-10" *ngIf="getInsideCompoundPrivilege()">
            <label class="form-label fw-bold text-start d-block">Compound Name
              <span class="required"></span>
            </label>
            <input
              type="text"
              class="form-control"
              formControlName="compoundName"
              placeholder="Enter Compound Name"
              [ngClass]="{
                'is-invalid': getCurrentForm().get('compoundName')?.touched && getCurrentForm().get('compoundName')?.invalid
              }"
            />
            <div
              *ngIf="getCurrentForm().get('compoundName')?.touched && getCurrentForm().get('compoundName')?.invalid"
              class="invalid-feedback d-block"
            >
              Compound Name is required.
            </div>
          </div>

          <!-- Detailed Address -->
          <div class="mb-10" *ngIf="getSellInsideCompoundInputs() || getRentOutInsideCompoundInputs() || getRentOutsideCompoundInputs() || getSellOutsideCompoundInputs()">
            <label class="form-label fw-bold text-start d-block">Detailed Address
              <span class="required"></span>
            </label>
            <input
              type="text"
              class="form-control"
              formControlName="detailedAddress"
              placeholder="Enter Detailed Address"
              [ngClass]="{
                'is-invalid': getCurrentForm().get('detailedAddress')?.touched && getCurrentForm().get('detailedAddress')?.invalid
              }"
            />
            <div
              *ngIf="getCurrentForm().get('detailedAddress')?.touched && getCurrentForm().get('detailedAddress')?.invalid"
              class="invalid-feedback d-block"
            >
              Detailed Address is required.
            </div>
          </div>

          <!-- Detailed Address Link -->
          <div class="mb-10" *ngIf="getSellInsideCompoundInputs() || getRentOutInsideCompoundInputs() || getRentOutsideCompoundInputs() || getSellOutsideCompoundInputs()">
            <label class="form-label fw-bold text-start d-block">Detailed Address Link</label>
            <input
              type="url"
              class="form-control"
              formControlName="addressLink"
              placeholder="Enter Address Link (https://...)"
              [ngClass]="{
                'is-invalid': getCurrentForm().get('addressLink')?.touched && getCurrentForm().get('addressLink')?.invalid
              }"
            />
            <div
              *ngIf="getCurrentForm().get('addressLink')?.touched && getCurrentForm().get('addressLink')?.invalid"
              class="invalid-feedback d-block"
            >
              <ng-container *ngIf="getCurrentForm().get('addressLink')?.errors?.['pattern']">
                Please enter a valid URL.
              </ng-container>
            </div>
          </div>

          <!-- Project Management -->
          <div class="mb-10" *ngIf="getSellInsideCompoundInputs()">
            <label class="form-label fw-bold text-start d-block">Project Management</label>
            <input
              type="text"
              class="form-control"
              formControlName="projectManagement"
              placeholder="Enter Project Management"
              [ngClass]="{
                'is-invalid': getCurrentForm().get('projectManagement')?.touched && getCurrentForm().get('projectManagement')?.invalid
              }"
            />
            <div
              *ngIf="getCurrentForm().get('projectManagement')?.touched && getCurrentForm().get('projectManagement')?.invalid"
              class="invalid-feedback d-block"
            >
              Project Management is required.
            </div>
          </div>

          <!-- Project Constructor -->
          <div class="mb-10" *ngIf="getSellInsideCompoundInputs()">
            <label class="form-label fw-bold text-start d-block">Project Constructor</label>
            <input
              type="text"
              class="form-control"
              formControlName="projectConstructor"
              placeholder="Enter Project Constructor"
              [ngClass]="{
                'is-invalid': getCurrentForm().get('projectConstructor')?.touched && getCurrentForm().get('projectConstructor')?.invalid
              }"
            />
            <div
              *ngIf="getCurrentForm().get('projectConstructor')?.touched && getCurrentForm().get('projectConstructor')?.invalid"
              class="invalid-feedback d-block"
            >
              Project Constructor is required.
            </div>
          </div>

          <!-- Dynamic Inputs for Step 2 -->
          <div *ngFor="let input of currentInputs; trackBy: trackByInputName" class="mb-7">
            <!-- Skip the hardcoded fields to avoid duplication -->
            <ng-container *ngIf="!['cityId', 'areaId', 'subAreaId', 'compoundName', 'detailedAddress', 'addressLink', 'projectManagement', 'projectConstructor', 'locationSuggestions'].includes(input.name)">
              <label class="form-label fw-bold text-start d-flex align-items-center flex-wrap gap-2 mb-3">
                <span>{{ input.label }}</span>
                <span *ngIf="isInputRequired(input)" class="required"></span>
              </label>
              <ng-container [ngSwitch]="input.type">
                              <!-- Enhanced Checkbox Input -->
              <div *ngSwitchCase="'checkbox'" class="custom-checkbox-container"
                   [class.has-error]="getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid">
                <input
                  type="checkbox"
                  class="custom-checkbox-input"
                  [formControlName]="input.name"
                  [id]="input.name"
                  [attr.aria-describedby]="input.name + '_help'"
                  [attr.aria-invalid]="getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid ? 'true' : 'false'"
                />
                <label class="custom-checkbox-label" [for]="input.name">
                  <div class="custom-checkbox-box">
                    <i class="fas fa-check custom-checkbox-icon"></i>
                  </div>
                  <div class="custom-checkbox-content">
                    <span class="custom-checkbox-text">{{ input.label }}</span>
                    <small *ngIf="input.name === 'locationSuggestions'" class="custom-checkbox-description">
                      Get personalized location recommendations based on your preferences
                    </small>
                    <small *ngIf="input.name === 'locationSuggestion'" class="custom-checkbox-description">
                      Get personalized location recommendations for your purchase requirements
                    </small>
                    <small *ngIf="input.name === 'budgetSuggestions'" class="custom-checkbox-description">
                      Receive budget optimization suggestions from our experts
                    </small>
                    <small *ngIf="input.name === 'rentPriceSuggestions'" class="custom-checkbox-description">
                      Get market-based pricing recommendations for your property
                    </small>
                  </div>
                </label>
                <!-- Enhanced validation feedback -->
                <div
                  *ngIf="getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid"
                  class="checkbox-error-feedback"
                  [id]="input.name + '_help'"
                  role="alert"
                >
                  <i class="fas fa-exclamation-circle me-1"></i>
                  <span>{{ input.label }} is required to proceed</span>
                </div>
              </div>

                <!-- Text Input -->
                <input
                  *ngSwitchCase="'text'"
                  type="text"
                  class="form-control"
                  [formControlName]="input.name"
                  [placeholder]="'Enter ' + input.label"
                  [ngClass]="{
                    'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid
                  }"
                />

                <!-- Number Input -->
                <input
                  *ngSwitchCase="'number'"
                  type="number"
                  class="form-control"
                  [formControlName]="input.name"
                  [placeholder]="'Enter ' + input.label"
                  min="0"
                  [ngClass]="{
                    'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid
                  }"
                />

                <!-- URL Input -->
                <input
                  *ngSwitchCase="'url'"
                  type="url"
                  class="form-control"
                  [formControlName]="input.name"
                  [placeholder]="'Enter ' + input.label"
                  [ngClass]="{
                    'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid
                  }"
                />

                <!-- Select Input -->
                <div *ngSwitchCase="'select'" class="dropdown">
                  <button
                    class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                    type="button"
                    [id]="input.name + 'Dropdown'"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                  >
                    <span>{{
                      getText(input.options, getCurrentForm().get(input.name)?.value) || 'Select ' + input.label
                    }}</span>
                    <i class="fas fa-chevron-down"></i>
                  </button>
                  <ul
                    class="dropdown-menu w-100"
                    [attr.aria-labelledby]="input.name + 'Dropdown'"
                    [ngStyle]="input.options && input.options.length > 10 ? { 'max-height': '200px', 'overflow-y': 'auto' } : {}"
                  >
                    <li *ngFor="let option of input.options">
                      <a
                        class="dropdown-item"
                        (click)="select(getCurrentForm(), input.name, option.value)"
                        >{{ option.key }}</a
                      >
                    </li>
                  </ul>
                </div>

                <!-- Textarea Input -->
                <textarea
                  *ngSwitchCase="'textarea'"
                  class="form-control"
                  [formControlName]="input.name"
                  [placeholder]="'Enter ' + input.label"
                  rows="3"
                  [ngClass]="{
                    'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid
                  }"
                ></textarea>
              </ng-container>

              <!-- Error message for dynamic inputs -->
              <div
                *ngIf="getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid"
                class="invalid-feedback d-block"
              >
                <ng-container *ngIf="getCurrentForm().get(input.name)?.errors?.['required']">
                  {{ input.label }} is required.
                </ng-container>
                <ng-container *ngIf="getCurrentForm().get(input.name)?.errors?.['pattern']">
                  Please enter a valid {{ input.label }}.
                </ng-container>
                <ng-container *ngIf="getCurrentForm().get(input.name)?.errors?.['min']">
                  {{ input.label }} must be greater than or equal to {{ getCurrentForm().get(input.name)?.errors?.['min']?.min }}.
                </ng-container>
              </div>
            </ng-container>
          </div>
        </div>

        <!-- Steps 3–5: Dynamic Inputs from Service -->
        <div *ngIf="currentStep > 2">
          <div *ngFor="let input of currentInputs; trackBy: trackByInputName" class="mb-7">
            <label class="form-label fw-bold text-start d-flex align-items-center flex-wrap gap-2 mb-3">
              <span>{{ input.label }}</span>
              <span *ngIf="isInputRequired(input)" class="required"></span>
            </label>
            <ng-container [ngSwitch]="input.type">
              <!-- Enhanced Checkbox Input -->
              <div *ngSwitchCase="'checkbox'" class="custom-checkbox-container"
                   [class.has-error]="getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid">
                <input
                  type="checkbox"
                  class="custom-checkbox-input"
                  [formControlName]="input.name"
                  [id]="input.name"
                  [attr.aria-describedby]="input.name + '_help'"
                  [attr.aria-invalid]="getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid ? 'true' : 'false'"
                />
                <label class="custom-checkbox-label" [for]="input.name">
                  <div class="custom-checkbox-box">
                    <i class="fas fa-check custom-checkbox-icon"></i>
                  </div>
                  <div class="custom-checkbox-content">
                    <span class="custom-checkbox-text">{{ input.label }}</span>
                    <small *ngIf="input.name === 'locationSuggestions'" class="custom-checkbox-description">
                      Get personalized location recommendations based on your preferences
                    </small>
                    <small *ngIf="input.name === 'budgetSuggestions'" class="custom-checkbox-description">
                      Receive budget optimization suggestions from our experts
                    </small>
                    <small *ngIf="input.name === 'rentPriceSuggestions'" class="custom-checkbox-description">
                      Get market-based pricing recommendations for your property
                    </small>
                  </div>
                </label>
                <!-- Enhanced validation feedback -->
                <div
                  *ngIf="getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid"
                  class="checkbox-error-feedback"
                  [id]="input.name + '_help'"
                  role="alert"
                >
                  <i class="fas fa-exclamation-circle me-1"></i>
                  <span>{{ input.label }} is required to proceed</span>
                </div>
              </div>

              <!-- Text Input -->
              <input
                *ngSwitchCase="'text'"
                type="text"
                class="form-control"
                [formControlName]="input.name"
                [placeholder]="'Enter ' + input.label"
                [ngClass]="{
                  'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid
                }"
              />

              <!-- Number Input -->
              <input
                *ngSwitchCase="'number'"
                type="number"
                class="form-control"
                [formControlName]="input.name"
                [placeholder]="'Enter ' + input.label"
                min="0"
                [ngClass]="{
                  'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid
                }"
              />

              <!-- Select Input -->
              <div *ngSwitchCase="'select'" class="dropdown">
                <button
                  class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                  type="button"
                  [id]="input.name + 'Dropdown'"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <span>{{
                    getText(input.options, getCurrentForm().get(input.name)?.value) || 'Select ' + input.label
                  }}</span>
                  <i class="fas fa-chevron-down"></i>
                </button>
                <ul
                  class="dropdown-menu w-100"
                  [attr.aria-labelledby]="input.name + 'Dropdown'"
                  [ngStyle]="input.options && input.options.length > 10 ? { 'max-height': '200px', 'overflow-y': 'auto' } : {}"
                >
                  <li *ngFor="let option of input.options">
                    <a
                      class="dropdown-item"
                      (click)="select(getCurrentForm(), input.name, option.value)"
                      >{{ option.key }}</a
                    >
                  </li>
                </ul>
              </div>

              <!-- Enhanced MultiSelect Input -->
              <div *ngSwitchCase="'multiSelect'" class="enhanced-multiselect">
                <div class="dropdown">
                  <button
                    class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center enhanced-multiselect-button"
                    type="button"
                    [id]="input.name + 'Dropdown'"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                    [class.has-selections]="getSelectedCount(input.name) > 0"
                  >
                    <div class="multiselect-display">
                      <span class="multiselect-text">{{
                        input.name === 'otherAccessories'
                          ? getSelectedAccessoriesText()
                          : input.name === 'otherExpenses'
                          ? getSelectedOtherExpensesText()
                          : getSelectedText(input.name, input.options || []) || 'Select ' + input.label
                      }}</span>
                      <span *ngIf="getSelectedCount(input.name) > 0" class="badge bg-primary ms-2">
                        {{ getSelectedCount(input.name) }}
                      </span>
                    </div>
                    <i class="fas fa-chevron-down multiselect-icon"></i>
                  </button>

                  <div class="dropdown-menu w-150 enhanced-multiselect-menu" [attr.aria-labelledby]="input.name + 'Dropdown'">

                    <!-- Select All / Clear All Controls -->
                    <div class="multiselect-controls p-2 border-bottom bg-light">
                      <div class="d-flex justify-content-between align-items-center">
                        <div class="form-check">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            [id]="input.name + '_selectAll'"
                            [checked]="areAllOptionsSelected(input.name)"
                            [indeterminate]="areSomeOptionsSelected(input.name) && !areAllOptionsSelected(input.name)"
                            (change)="toggleSelectAll(input.name)"
                          >
                          <label class="form-check-label fw-bold text-primary" [for]="input.name + '_selectAll'">
                            Select All
                          </label>
                        </div>
                      </div>
                    </div>

                    <!-- Options List -->
                    <div class="multiselect-options-container" style="max-height: 250px; overflow-y: auto;">
                      <div class="p-2">
                        <div *ngFor="let option of getFilteredOptions(input.name, input.options || [])" class="multiselect-option mb-1">
                          <div class="form-check">
                            <input
                              class="form-check-input"
                              type="checkbox"
                              [id]="input.name + '_' + option.value"
                              [checked]="
                                input.name === 'otherAccessories'
                                  ? isAccessorySelected(option.value)
                                  : input.name === 'otherExpenses'
                                  ? isOtherExpenseSelected(option.value)
                                  : isMultiSelectOptionSelected(input.name, option.value)
                              "
                              (change)="
                                input.name === 'otherAccessories'
                                  ? toggleAccessory(option.value)
                                  : input.name === 'otherExpenses'
                                  ? toggleOtherExpense(option.value)
                                  : toggleMultiSelect(input.name, option.value)
                              "
                            >
                            <label
                              class="form-check-label text-start multiselect-option-label"
                              [for]="input.name + '_' + option.value"
                              [class.fw-bold]="isMultiSelectOptionSelected(input.name, option.value)"
                            >
                              {{ option.key }}
                            </label>
                          </div>
                        </div>

                        <!-- No Results Message -->
                        <div *ngIf="getFilteredOptions(input.name, input.options || []).length === 0" class="text-center text-muted py-3">
                          <i class="fas fa-search mb-2"></i>
                          <div>No options found</div>
                          <small>Try adjusting your search</small>
                        </div>
                      </div>
                    </div>

                  </div>
                </div>
              </div>

              <!-- File Input -->
              <div *ngSwitchCase="'file'" class="mb-4 upload-card-container">
                <div
                  class="card cursor-pointer upload-card"
                  [class.has-files]="getFileCount(input.name) > 0"
                >
                  <label [for]="input.name" class="card-body text-center">
                    <div class="upload-icon">
                      <i
                        class="fas fa-2x"
                        [class.fa-cloud-upload-alt]="getFileCount(input.name) === 0"
                        [class.fa-check-circle]="getFileCount(input.name) > 0"
                      ></i>
                    </div>
                    <div class="upload-content">
                      <h6 class="upload-title mb-2">{{ input.label }}</h6>
                      <p class="upload-subtitle mb-0" *ngIf="getFileCount(input.name) === 0">
                        Click to upload {{ input.name === 'video' ? 'videos' : 'images' }}
                      </p>
                      <p class="upload-subtitle mb-0" *ngIf="getFileCount(input.name) > 0">
                        {{ getFileCount(input.name) }} file(s) uploaded successfully
                      </p>
                      <span *ngIf="getFileCount(input.name) > 0" class="badge bg-success mt-2">
                        {{ getFileCount(input.name) }} file(s)
                      </span>
                    </div>
                    <input
                      type="file"
                      [id]="input.name"
                      class="d-none"
                      (change)="onFileChange($event, input.name)"
                      [multiple]="input.name !== 'mainImage'"
                      [accept]="input.name === 'video' ? 'video/*' : 'image/*'"
                    />
                  </label>
                </div>
              </div>

              <!-- Textarea for Notes -->
              <textarea
                *ngSwitchCase="'textarea'"
                class="form-control"
                [formControlName]="input.name"
                rows="4"
                [placeholder]="'Enter ' + input.label"
                [ngClass]="{
                  'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid
                }"
              ></textarea>
            </ng-container>

            <!-- Enhanced Error Display -->
            <div
              *ngIf="getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid"
              class="invalid-feedback d-block"
            >
              <div *ngIf="getCurrentForm().get(input.name)?.errors?.['required']">
                {{ input.label }} is required.
              </div>
              <div *ngIf="getCurrentForm().get(input.name)?.errors?.['min']">
                {{ input.label }} must be greater than or equal to {{ getCurrentForm().get(input.name)?.errors?.['min']?.min }}.
              </div>
              <div *ngIf="getCurrentForm().get(input.name)?.errors?.['pattern']">
                Please enter a valid {{ input.label.toLowerCase() }}.
              </div>
            </div>
          </div>
        </div>

        <!-- Step 4 No Inputs Info Card -->
        <div *ngIf="currentStep === 4 && !hasStepInputs()" class="alert alert-info mt-4">
          <div class="d-flex align-items-center">
            <i class="fas fa-info-circle fa-2x me-3 text-info"></i>
            <div>
              <h6 class="alert-heading mb-1">No Files to Upload</h6>
              <p class="mb-0">{{ getStep4InfoMessage() }}</p>
            </div>
          </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="d-flex justify-content-between align-items-center pt-10">
          <!-- Back Button -->
          <button
            *ngIf="currentStep > 1"
            type="button"
            class="btn btn-lg btn-outline-success btn-light-success px-6 py-3 rounded-pill"
            (click)="prevStep()"
          >
            <i class="fas fa-arrow-left me-2"></i>
            Back
          </button>
          <div *ngIf="currentStep === 1" class="invisible"></div>

          <!-- Next/Submit Button -->
          <button
            *ngIf="currentStep !== totalSteps"
            type="button"
            class="btn btn-lg btn-navy px-6 py-3 rounded-pill"
            [disabled]="!isCurrentFormValid()"
            (click)="nextStep()"
          >
            <span class="indicator-label text-white">
              Next
              <i class="fas fa-arrow-right ms-2 text-white"></i>
            </span>
          </button>

          <button
            *ngIf="currentStep === totalSteps"
            type="button"
            class="btn btn-lg btn-navy px-6 py-3 rounded-pill d-flex align-items-center"
            [disabled]="!isCurrentFormValid() || isSubmitting"
            (click)="submitForm()"
          >
            <span *ngIf="!isSubmitting" class="indicator-label text-white">
              <i class="fas fa-check me-2"></i>
              Submit
            </span>
            <span *ngIf="isSubmitting" class="d-flex align-items-center text-white">
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Submitting...
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
